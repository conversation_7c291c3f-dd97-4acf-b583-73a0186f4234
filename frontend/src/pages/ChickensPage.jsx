import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { useFarm } from '../contexts/FarmContext';
import DashboardLayout from '../components/DashboardLayout';
import ChickenList from '../components/ChickenList';
import ChickenForm from '../components/ChickenForm';
import { Card, CardContent } from '../components/ui/Card';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';
import Modal from '../components/ui/Modal';
import axios from 'axios';

const ChickensPage = () => {
  const { t } = useTranslation();
  const { token, user } = useAuth();
  const { selectedFarm } = useFarm();
  const [chickens, setChickens] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editingChicken, setEditingChicken] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  const canManage = user && (user.roles.includes('admin') || user.roles.includes('manager'));

  useEffect(() => {
    const fetchChickens = async () => {
      if (!selectedFarm || !token) {
        setLoading(false);
        return;
      }
      try {
        const config = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };
        const res = await axios.get(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/chickens`, config);
        setChickens(res.data);
      } catch (err) {
        console.error('Error fetching chickens:', err.response ? err.response.data : err.message);
        setError(err.response?.data?.message || t('failed_to_fetch_chickens'));
      } finally {
        setLoading(false);
      }
    };

    fetchChickens();
  }, [selectedFarm, token]);

  const handleAddChicken = async (chickenData) => {
    try {
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
      const res = await axios.post(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/chickens`, chickenData, config);
      setChickens([...chickens, res.data.chicken]);
      setEditingChicken(null); // Clear form after adding
    } catch (err) {
      console.error('Error adding chicken:', err.response ? err.response.data : err.message);
      setError(err.response?.data?.message || t('failed_to_save_chicken'));
    }
  };

  const handleUpdateChicken = async (id, chickenData) => {
    try {
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
      const res = await axios.put(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/chickens/${id}`, chickenData, config);
      setChickens(chickens.map((chicken) => (chicken._id === id ? res.data.chicken : chicken)));
      setEditingChicken(null); // Clear form after updating
    } catch (err) {
      console.error('Error updating chicken:', err.response ? err.response.data : err.message);
      setError(err.response?.data?.message || t('failed_to_save_chicken'));
    }
  };

  const handleDeleteChicken = async (id) => {
    if (window.confirm(t('confirm_delete_chicken'))) {
      try {
        const config = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };
        await axios.delete(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/chickens/${id}`, config);
        setChickens(chickens.filter((chicken) => chicken._id !== id));
      } catch (err) {
        console.error('Error deleting chicken:', err.response ? err.response.data : err.message);
        setError(err.response?.data?.message || t('failed_to_delete_chicken'));
      }
    }
  };

  // Filter chickens based on search and status
  const filteredChickens = chickens.filter(chicken => {
    const matchesSearch = chicken.breed?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         chicken.notes?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || chicken.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const statusCounts = chickens.reduce((acc, chicken) => {
    acc[chicken.status] = (acc[chicken.status] || 0) + 1;
    return acc;
  }, {});

  if (!selectedFarm) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-neutral-200 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-neutral-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-neutral-900 mb-2">{t('chickens')}</h2>
          <p className="text-neutral-600">{t('no_farm_selected')}</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-4xl font-bold text-neutral-900 font-display mb-2">
              🐔 {t('chickens')}
            </h2>
            <p className="text-lg text-neutral-600">
              Manage your poultry flock with detailed tracking
            </p>
          </div>
          {canManage && (
            <Button
              onClick={() => {
                setEditingChicken({});
                setIsModalOpen(true);
              }}
              size="lg"
              className="shadow-medium"
              data-testid="add-new-chicken-button"
            >
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              {t('add_new_chicken')}
            </Button>
          )}
        </div>

        {/* Status Overview Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-primary-600">{chickens.length}</div>
              <div className="text-sm text-neutral-600">Total Chickens</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{statusCounts.healthy || 0}</div>
              <div className="text-sm text-neutral-600">Healthy</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-yellow-600">{statusCounts.laying || 0}</div>
              <div className="text-sm text-neutral-600">Laying</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-red-600">{statusCounts.sick || 0}</div>
              <div className="text-sm text-neutral-600">Sick</div>
            </CardContent>
          </Card>
        </div>
      </div>

      {error && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span className="text-red-700">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Filter */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search chickens by breed or notes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
            <div className="flex gap-2">
              {['all', 'healthy', 'laying', 'sick', 'sold'].map((status) => (
                <Button
                  key={status}
                  variant={filterStatus === status ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterStatus(status)}
                >
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                  {status !== 'all' && statusCounts[status] && (
                    <Badge variant="secondary" size="sm" className="ml-2">
                      {statusCounts[status]}
                    </Badge>
                  )}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chickens List */}
      {loading ? (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="animate-spin w-8 h-8 border-4 border-primary-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-neutral-600">{t('loading')}...</p>
          </CardContent>
        </Card>
      ) : (
        <ChickenList
          chickens={filteredChickens}
          onEdit={canManage ? (chicken) => {
            setEditingChicken(chicken);
            setIsModalOpen(true);
          } : null}
          onDelete={canManage ? handleDeleteChicken : null}
        />
      )}

      {/* Modal for Add/Edit Chicken */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setEditingChicken(null);
        }}
        title={editingChicken?._id ? 'Edit Chicken' : 'Add New Chicken'}
        size="lg"
      >
        {editingChicken && (
          <ChickenForm
            initialData={editingChicken}
            onSubmit={async (data) => {
              if (editingChicken._id) {
                await handleUpdateChicken(editingChicken._id, data);
              } else {
                await handleAddChicken(data);
              }
              setIsModalOpen(false);
              setEditingChicken(null);
            }}
            onCancel={() => {
              setIsModalOpen(false);
              setEditingChicken(null);
            }}
          />
        )}
      </Modal>
    </DashboardLayout>
  );
};

export default ChickensPage;
