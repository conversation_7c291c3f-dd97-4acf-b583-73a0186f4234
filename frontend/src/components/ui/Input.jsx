import React from 'react';
import { cn } from '../../utils/cn';

const Input = React.forwardRef(({
  className,
  type = 'text',
  error = false,
  label,
  helperText,
  icon,
  ...props
}, ref) => {
  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-neutral-700 mb-2">
          {label}
        </label>
      )}
      <div className="relative">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div className="h-5 w-5 text-neutral-400">
              {icon}
            </div>
          </div>
        )}
        <input
          type={type}
          className={cn(
            'block w-full rounded-lg border border-neutral-300 bg-white px-3 py-2.5',
            'text-neutral-900 placeholder-neutral-500',
            'focus:border-primary-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-20',
            'transition-all duration-200 ease-in-out',
            'shadow-soft hover:shadow-medium',
            'disabled:bg-neutral-50 disabled:text-neutral-500 disabled:cursor-not-allowed',
            icon && 'pl-10',
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
            className
          )}
          ref={ref}
          {...props}
        />
      </div>
      {helperText && (
        <p className={cn(
          'mt-2 text-sm',
          error ? 'text-red-600' : 'text-neutral-600'
        )}>
          {helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
