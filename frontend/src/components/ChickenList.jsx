import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { Card, CardContent } from './ui/Card';
import Button from './ui/Button';
import Badge from './ui/Badge';

const ChickenList = ({ chickens, onEdit, onDelete }) => {
  const { t } = useTranslation();
  const { user } = useAuth();

  const canManage = user && (user.roles.includes('admin') || user.roles.includes('manager'));

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-xl font-semibold text-gray-800 mb-4">{t('chickens')}</h3>
      {chickens.length === 0 ? (
        <p>{t('no_chickens_found')}</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('breed')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('hatch_date')}</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('status')}</th>
                {canManage && <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{t('actions')}</th>}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {chickens.map((chicken) => (
                <tr key={chicken._id}>
                  <td className="px-6 py-4 whitespace-nowrap">{chicken.breed}</td>
                  <td className="px-6 py-4 whitespace-nowrap">{new Date(chicken.hatchDate).toLocaleDateString()}</td>
                  <td className="px-6 py-4 whitespace-nowrap">{t(chicken.status)}</td>
                  {canManage && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => onEdit(chicken)}
                        className="text-indigo-600 hover:text-indigo-900 mr-4"
                      >
                        {t('edit')}
                      </button>
                      <button
                        onClick={() => onDelete(chicken._id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        {t('delete')}
                      </button>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ChickenList;
