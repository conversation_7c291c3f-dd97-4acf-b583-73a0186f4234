{"address":"127.0.0.1","code":"ECONNREFUSED","environment":"development","errno":-61,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"smartpoultry-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-08-03T17:31:45.129Z"}
{"address":"127.0.0.1","code":"ECONNREFUSED","environment":"development","errno":-61,"level":"error","message":"Redis connection error: connect ECONNREFUSED 127.0.0.1:6379","port":6379,"service":"smartpoultry-backend","stack":"Error: connect ECONNREFUSED 127.0.0.1:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-08-03T17:33:08.996Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at sanitizeInput (/Users/<USER>/SmartPoultry/backend/src/middleware/security.js:82:18)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at internalNext (/Users/<USER>/SmartPoultry/backend/node_modules/helmet/index.cjs:531:6)","timestamp":"2025-08-04T17:06:45.438Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at sanitizeInput (/Users/<USER>/SmartPoultry/backend/src/middleware/security.js:87:3)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at internalNext (/Users/<USER>/SmartPoultry/backend/node_modules/helmet/index.cjs:531:6)","timestamp":"2025-08-04T17:08:28.908Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:10.841Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:25.977Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:27.219Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:27.834Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:28.200Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:45.144Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:45.769Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:46.134Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:46.317Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:46.483Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:46.665Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:46.815Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:47.000Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:47.149Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:47.317Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:47.533Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:47.701Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:47.883Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:48.086Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-04T18:24:48.583Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:10:04.753Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:10:14.191Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:10:40.556Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:11:46.628Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:11:47.399Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:13:40.202Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:13:40.847Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T04:13:45.638Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:16.122Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:17.876Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:17.878Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:19.376Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:20.732Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:24.623Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:29.637Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:34.643Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:39.653Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:44.661Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:49.670Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:52.761Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:52.765Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:53.366Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:54.913Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:14:59.931Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:04.958Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:09.977Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:14.991Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:20.012Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:25.030Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:30.054Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:35.086Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:40.096Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:40.373Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:15:40.384Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:45:54.195Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:45:59.210Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:46:10.234Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:46:22.596Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:46:32.519Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:46:34.603Z"}
{"environment":"development","level":"error","message":"Unhandled error: Cannot set property query of #<IncomingMessage> which has only a getter","service":"smartpoultry-backend","stack":"TypeError: Cannot set property query of #<IncomingMessage> which has only a getter\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:113:18\n    at Array.forEach (<anonymous>)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/express-mongo-sanitize/index.js:110:44\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)\n    at trimPrefix (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:342:13)\n    at /Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:297:9\n    at processParams (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:582:12)\n    at next (/Users/<USER>/SmartPoultry/backend/node_modules/router/index.js:291:5)\n    at cookieParser (/Users/<USER>/SmartPoultry/backend/node_modules/cookie-parser/index.js:57:14)\n    at Layer.handleRequest (/Users/<USER>/SmartPoultry/backend/node_modules/router/lib/layer.js:152:17)","timestamp":"2025-08-05T05:47:11.862Z"}
